#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音视频爬虫快速启动脚本

这是一个简化的启动脚本，可以快速配置和运行抖音视频爬虫
"""

import asyncio
import json
import os
import sys
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import config
from main import main as crawler_main


def setup_search_config(keywords="美食,旅游,科技", max_count=20):
    """设置关键词搜索配置 - 极强反反爬措施"""
    config.PLATFORM = "dy"  # 抖音平台
    config.KEYWORDS = keywords  # 搜索关键词
    config.CRAWLER_TYPE = "search"  # 搜索模式
    config.CRAWLER_MAX_NOTES_COUNT = min(max_count, 10)  # 限制最大数量为10
    config.ENABLE_GET_MEIDAS = False  # 不下载媒体文件
    config.ENABLE_GET_COMMENTS = False  # 不爬取评论
    config.SAVE_DATA_OPTION = "json"  # 保存为JSON格式
    config.LOGIN_TYPE = "qrcode"  # 二维码登录
    config.HEADLESS = False  # 显示浏览器窗口
    config.CRAWLER_MAX_SLEEP_SEC = 15  # 大幅增加到15秒间隔
    config.MAX_CONCURRENCY_NUM = 1  # 单线程爬取
    config.ENABLE_IP_PROXY = False  # 不使用代理
    config.CRAWLER_MAX_SLEEP_SEC_RANDOM = 5  # 随机延迟0-5秒

    # 额外的反反爬设置
    if hasattr(config, 'ENABLE_RANDOM_SLEEP'):
        config.ENABLE_RANDOM_SLEEP = True
    if hasattr(config, 'MIN_SLEEP_SEC'):
        config.MIN_SLEEP_SEC = 10
    if hasattr(config, 'MAX_SLEEP_SEC'):
        config.MAX_SLEEP_SEC = 20


def setup_creator_config(creator_id, max_count=20):
    """设置抖音号专门爬取配置 - 终极反反爬措施"""
    config.PLATFORM = "dy"  # 抖音平台
    config.CRAWLER_TYPE = "creator"  # 创作者模式
    config.CRAWLER_MAX_NOTES_COUNT = min(max_count, 3)  # 极限制最大数量为3
    config.ENABLE_GET_MEIDAS = False  # 不下载媒体文件
    config.ENABLE_GET_COMMENTS = False  # 不爬取评论
    config.SAVE_DATA_OPTION = "json"  # 保存为JSON格式
    config.LOGIN_TYPE = "qrcode"  # 二维码登录
    config.HEADLESS = False  # 显示浏览器窗口
    config.CRAWLER_MAX_SLEEP_SEC = 60  # 极大增加爬取间隔到60秒
    config.MAX_CONCURRENCY_NUM = 1  # 设置并发数为1
    config.ENABLE_IP_PROXY = False  # 不使用代理
    config.CRAWLER_MAX_SLEEP_SEC_RANDOM = 30  # 随机延迟0-30秒

    # 终极反反爬设置
    if hasattr(config, 'ENABLE_RANDOM_SLEEP'):
        config.ENABLE_RANDOM_SLEEP = True
    if hasattr(config, 'MIN_SLEEP_SEC'):
        config.MIN_SLEEP_SEC = 45  # 最小延迟45秒
    if hasattr(config, 'MAX_SLEEP_SEC'):
        config.MAX_SLEEP_SEC = 90  # 最大延迟90秒

    # 新增反反爬设置
    if hasattr(config, 'ENABLE_STEALTH_MODE'):
        config.ENABLE_STEALTH_MODE = True
    if hasattr(config, 'USER_AGENT_RANDOM'):
        config.USER_AGENT_RANDOM = True
    if hasattr(config, 'ENABLE_VIEWPORT_RANDOM'):
        config.ENABLE_VIEWPORT_RANDOM = True
    if hasattr(config, 'REQUEST_DELAY_MIN'):
        config.REQUEST_DELAY_MIN = 20
    if hasattr(config, 'REQUEST_DELAY_MAX'):
        config.REQUEST_DELAY_MAX = 40
    if hasattr(config, 'RETRY_COUNT'):
        config.RETRY_COUNT = 3
    if hasattr(config, 'RETRY_DELAY'):
        config.RETRY_DELAY = 60  # 重试间隔60秒

    # 设置要爬取的创作者ID列表
    config.DY_CREATOR_ID_LIST = [creator_id]


def print_banner():
    """打印欢迎信息"""
    print("🎬" + "=" * 60 + "🎬")
    print("           抖音视频链接爬取工具 v2.0")
    print("🎬" + "=" * 60 + "🎬")
    print()
    print("功能说明:")
    print("✅ 通过关键词搜索抖音视频")
    print("✅ 专门爬取指定抖音号的所有视频")
    print("✅ 获取视频页面链接和下载链接")
    print("✅ 获取视频基本信息（标题、作者、点赞数等）")
    print("✅ 支持多种输出格式")
    print("✅ 结果保存为JSON格式，方便查看")
    print()


def choose_crawl_mode():
    """选择爬取模式"""
    print("🎯 选择爬取模式:")
    print("=" * 50)
    print("1. 🔍 关键词搜索模式")
    print("   - 通过关键词搜索相关视频")
    print("   - 可以发现不同作者的视频")
    print("   - 适合寻找特定主题的内容")
    print()
    print("2. 👤 抖音号专门爬取模式")
    print("   - 专门爬取指定抖音号的所有视频")
    print("   - 获取该作者的完整作品列表")
    print("   - 适合关注特定创作者")
    print()

    while True:
        try:
            choice = input("请选择爬取模式 (1-2): ").strip()
            if choice in ['1', '2']:
                return int(choice)
            else:
                print("❌ 请输入 1 或 2")
        except KeyboardInterrupt:
            raise
        except:
            print("❌ 输入无效，请重新输入")


def get_creator_info():
    """获取抖音号信息 - 增强格式验证"""
    print("\n👤 抖音号专门爬取配置:")
    print("-" * 50)
    print("💡 支持的抖音号格式:")
    print("   1. 用户主页链接: https://www.douyin.com/user/MS4wLjABAAAA... (推荐)")
    print("   2. sec_user_id: MS4wLjABAAAA... (最准确)")
    print("   3. 数字抖音号: 59058596699 (可能需要手动转换)")
    print()
    print("⚠️  重要提醒:")
    print("   - 数字抖音号不是真正的用户ID，可能导致爬取到错误内容")
    print("   - 建议使用用户主页链接，确保爬取准确性")
    print("   - 如何获取主页链接: 抖音APP → 用户主页 → 分享 → 复制链接")
    print()

    creator_input = input("请输入抖音号、主页链接或sec_user_id: ").strip()
    if not creator_input:
        print("❌ 必须输入抖音号信息")
        return None

    # 增强的格式处理和验证
    if creator_input.startswith('https://www.douyin.com/user/'):
        # 从链接中提取sec_user_id
        creator_id = creator_input.split('/user/')[-1].split('?')[0]
        print(f"✅ 检测到用户主页链接，提取到 sec_user_id: {creator_id[:20]}...")
    elif creator_input.startswith('MS4wLjABAAAA'):
        # 直接是sec_user_id
        creator_id = creator_input
        print(f"✅ 检测到 sec_user_id: {creator_id[:20]}...")
    elif creator_input.isdigit():
        # 数字抖音号
        creator_id = creator_input
        print("⚠️  检测到数字抖音号")
        print("❌ 警告: 数字抖音号不是真正的用户ID，可能导致:")
        print("   - 爬取到其他用户的视频")
        print("   - 爬取失败或获取错误内容")
        print("   - 建议获取该用户的主页链接重新输入")

        confirm = input("\n是否继续使用数字抖音号? (y/N): ").strip().lower()
        if confirm != 'y':
            print("💡 请获取用户主页链接后重新运行程序")
            return None
    else:
        # 其他格式
        creator_id = creator_input
        print("⚠️  未识别的格式，将直接使用输入内容")
        print("💡 如果爬取结果不准确，请使用用户主页链接")

    return creator_id


def choose_link_types():
    """选择要提取的链接类型"""
    print("🔗 链接输出格式:")
    print("=" * 50)
    print("1. 📱 简洁链接列表 (推荐)")
    print("   - 只输出纯链接，一行一个")
    print("   - 格式最简洁，方便复制使用")
    print("   - 就像你的 douyin_links_only_xxx.txt 文件一样")
    print()
    print("2. 📋 带序号的链接")
    print("   - 每个链接前面有序号")
    print("   - 方便查看和引用")
    print()
    print("3. 📄 详细信息 + 链接")
    print("   - 包含标题、作者等信息")
    print("   - 适合需要了解视频内容的情况")
    print()

    while True:
        try:
            choice = input("请选择输出格式 (1-3，推荐选1): ").strip()
            if choice in ['1', '2', '3']:
                return int(choice)
            elif choice == '':
                return 1  # 默认选择简洁格式
            else:
                print("❌ 请输入 1-3 之间的数字")
        except KeyboardInterrupt:
            raise
        except:
            print("❌ 输入无效，请重新输入")


def choose_account_mode():
    """选择账号模式"""
    print("\n👤 账号登录选择:")
    print("=" * 50)
    print("1. 🔄 使用新账号登录")
    print("   - 清除之前的登录信息")
    print("   - 使用全新的抖音账号扫码登录")
    print("   - 推荐在遇到账号限制时使用")
    print()
    print("2. 📱 继续使用当前账号")
    print("   - 保持现有登录状态")
    print("   - 适合账号状态正常的情况")
    print()

    while True:
        try:
            choice = input("请选择账号模式 (1-2): ").strip()
            if choice in ['1', '2']:
                return int(choice)
            else:
                print("❌ 请输入 1 或 2")
        except KeyboardInterrupt:
            raise
        except:
            print("❌ 输入无效，请重新输入")


def clear_login_data():
    """清除登录数据，强制使用新账号"""
    import shutil

    print("\n🧹 正在清除旧的登录数据...")

    # 可能的登录数据存储位置
    data_dirs_to_clear = [
        "browser_data",
        "user_data",
        "profile",
        ".browser",
        "cookies",
        "session"
    ]

    cleared_count = 0
    for dir_name in data_dirs_to_clear:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✅ 已清除: {dir_name}")
                cleared_count += 1
            except Exception as e:
                print(f"⚠️  清除 {dir_name} 时出错: {e}")

    # 清除可能的cookie文件
    cookie_files = ["cookies.json", "session.json", "login.json"]
    for file_name in cookie_files:
        if os.path.exists(file_name):
            try:
                os.remove(file_name)
                print(f"✅ 已清除: {file_name}")
                cleared_count += 1
            except Exception as e:
                print(f"⚠️  清除 {file_name} 时出错: {e}")

    if cleared_count > 0:
        print(f"✅ 成功清除 {cleared_count} 个登录数据文件/目录")
        print("💡 现在将使用全新的账号登录")
    else:
        print("💡 没有找到需要清除的登录数据")

    print()


def print_instructions():
    """打印使用说明"""
    print("📋 使用说明:")
    print("1. 程序会自动打开浏览器")
    print("2. 请使用手机抖音APP扫码登录")
    print("3. 登录成功后，请先手动浏览几个视频（重要！）")
    print("4. 然后程序会自动开始爬取")
    print("5. 爬取完成后会根据你的选择提取相应链接")
    print("6. 结果会保存为文本文件，方便查看和使用")
    print()
    print("🛡️  终极反反爬提醒:")
    print("- 程序已配置终极反反爬措施（45-90秒间隔）")
    print("- 每次只爬取3个视频，避免触发限制")
    print("- 如遇到 'account blocked' 错误，程序会自动重试")
    print("- 建议在网络环境良好时使用")
    print("- 登录后务必先手动浏览几个视频，模拟正常用户行为")
    print()


def load_crawled_data():
    """加载爬取的数据"""
    data_dir = "data/douyin"
    results = []

    if not os.path.exists(data_dir):
        return results

    # 查找最新的JSON文件
    for root, _, files in os.walk(data_dir):
        for file in files:
            if file.endswith('.json') and 'contents' in file:
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        if content.startswith('['):
                            # JSON数组格式
                            data = json.loads(content)
                            results.extend(data)
                        else:
                            # JSONL格式
                            lines = content.split('\n')
                            for line in lines:
                                if line.strip():
                                    try:
                                        data = json.loads(line)
                                        results.append(data)
                                    except json.JSONDecodeError:
                                        continue
                except Exception as e:
                    print(f"读取文件 {file_path} 失败: {e}")

    return results


def validate_and_filter_creator_videos(data, target_creator_id):
    """验证并筛选指定创作者的视频"""
    if not data:
        return data, []

    print(f"\n🔍 验证视频是否属于目标创作者...")
    print(f"   目标创作者ID: {target_creator_id}")

    valid_videos = []
    invalid_videos = []

    for video in data:
        # 获取视频的创作者信息
        video_creator_id = video.get('sec_uid', '')
        video_user_id = str(video.get('user_id', ''))
        video_nickname = video.get('nickname', '')

        # 多重验证
        is_valid = False

        # 验证1: sec_uid匹配
        if video_creator_id == target_creator_id:
            is_valid = True

        # 验证2: 如果目标是数字抖音号，检查user_id
        elif target_creator_id.isdigit() and video_user_id == target_creator_id:
            is_valid = True

        # 验证3: 部分匹配（用于兼容性）
        elif target_creator_id in video_creator_id or target_creator_id in video_user_id:
            is_valid = True

        if is_valid:
            valid_videos.append(video)
        else:
            invalid_videos.append({
                'title': video.get('title', '无标题')[:50],
                'creator_id': video_creator_id,
                'user_id': video_user_id,
                'nickname': video_nickname
            })

    # 显示验证结果
    print(f"✅ 验证完成:")
    print(f"   📹 有效视频: {len(valid_videos)} 个")
    print(f"   ❌ 无效视频: {len(invalid_videos)} 个")

    if invalid_videos:
        print(f"\n⚠️  发现 {len(invalid_videos)} 个不属于目标创作者的视频:")
        for i, invalid in enumerate(invalid_videos[:5], 1):  # 只显示前5个
            print(f"   {i}. {invalid['title']} (作者: {invalid['nickname']})")
        if len(invalid_videos) > 5:
            print(f"   ... 还有 {len(invalid_videos) - 5} 个")

        print("\n💡 可能的原因:")
        print("   - 使用了数字抖音号而非真正的sec_user_id")
        print("   - 搜索结果包含了其他创作者的视频")
        print("   - 建议使用用户主页链接获取准确的sec_user_id")

    return valid_videos, invalid_videos


def extract_links_by_type(data, link_type):
    """根据类型提取链接"""
    if not data:
        print("❌ 没有找到爬取数据")
        return

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    if link_type == 1:  # 简洁链接列表
        extract_clean_links(data, timestamp)
    elif link_type == 2:  # 带序号的链接
        extract_numbered_links(data, timestamp)
    elif link_type == 3:  # 详细信息 + 链接
        extract_detailed_links(data, timestamp)


def extract_clean_links(data, timestamp):
    """提取简洁链接列表（就像你的douyin_links_only文件一样）"""
    filename = f"douyin_links_only_{timestamp}.txt"

    with open(filename, 'w', encoding='utf-8') as f:
        f.write("抖音视频标准链接\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("=" * 50 + "\n\n")

        # 只写链接，一行一个，最简洁的格式
        for video in data:
            url = video.get('aweme_url', '')
            if url:
                f.write(f"{url}\n")

    print(f"✅ 简洁链接已保存到: {filename}")
    print(f"📱 格式和你的 douyin_links_only_xxx.txt 完全一样")


def extract_numbered_links(data, timestamp):
    """提取带序号的链接"""
    filename = f"douyin_numbered_links_{timestamp}.txt"

    with open(filename, 'w', encoding='utf-8') as f:
        f.write("抖音视频链接 (带序号)\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"视频数量: {len(data)}\n")
        f.write("=" * 50 + "\n\n")

        for i, video in enumerate(data, 1):
            url = video.get('aweme_url', '')
            if url:
                f.write(f"{i:2d}. {url}\n")

    print(f"✅ 带序号链接已保存到: {filename}")


def extract_detailed_links(data, timestamp):
    """提取详细信息 + 链接"""
    filename = f"douyin_detailed_links_{timestamp}.txt"

    with open(filename, 'w', encoding='utf-8') as f:
        f.write("抖音视频详细信息\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"视频数量: {len(data)}\n")
        f.write("=" * 60 + "\n\n")

        for i, video in enumerate(data, 1):
            title = video.get('title', '无标题')
            author = video.get('nickname', '未知')
            url = video.get('aweme_url', '')
            likes = video.get('liked_count', 0)

            f.write(f"{i:2d}. {title}\n")
            f.write(f"    👤 作者: {author}\n")
            f.write(f"    👍 点赞: {likes}\n")
            f.write(f"    🔗 链接: {url}\n")
            f.write("-" * 60 + "\n")

    print(f"✅ 详细信息已保存到: {filename}")





async def main():
    """主函数"""
    print_banner()

    # 选择账号模式
    account_mode = choose_account_mode()

    if account_mode == 1:
        # 清除旧的登录数据，使用新账号
        clear_login_data()
        print("🔄 将使用新账号登录")
    else:
        print("📱 将继续使用当前账号")

    print()

    # 选择爬取模式
    crawl_mode = choose_crawl_mode()

    if crawl_mode == 1:
        # 关键词搜索模式
        print("\n🔍 关键词搜索模式")
        print("🔧 配置参数:")
        keywords = input("请输入搜索关键词（多个用逗号分隔，回车使用默认）: ").strip()
        if not keywords:
            keywords = "美食,旅游,科技"
            print(f"使用默认关键词: {keywords}")

        try:
            max_count = input("请输入最大爬取数量（回车使用默认20）: ").strip()
            max_count = int(max_count) if max_count else 20
        except ValueError:
            max_count = 20
            print(f"使用默认数量: {max_count}")

        print(f"\n✅ 搜索配置完成:")
        print(f"   模式: 关键词搜索")
        print(f"   关键词: {keywords}")
        print(f"   数量: {max_count}")

        # 选择链接类型
        link_type = choose_link_types()

        print_instructions()
        input("按回车键开始爬取...")

        # 设置搜索配置
        setup_search_config(keywords, max_count)

    elif crawl_mode == 2:
        # 抖音号专门爬取模式
        print("\n👤 抖音号专门爬取模式")
        creator_id = get_creator_info()
        if not creator_id:
            return

        try:
            max_count = input("请输入最大爬取数量（回车使用默认10，建议不超过20）: ").strip()
            max_count = int(max_count) if max_count else 10
            if max_count > 20:
                print("⚠️  警告: 数量过大可能增加被限制的风险")
                confirm = input("是否继续? (y/N): ").strip().lower()
                if confirm != 'y':
                    max_count = 10
                    print("已调整为安全数量: 10")
        except ValueError:
            max_count = 10
            print(f"使用默认数量: {max_count}")

        print(f"\n✅ 抖音号配置完成:")
        print(f"   模式: 抖音号专门爬取")
        print(f"   抖音号: {creator_id}")
        print(f"   数量: {max_count}")

        # 选择链接类型
        link_type = choose_link_types()

        print_instructions()
        input("按回车键开始爬取...")

        # 设置创作者配置
        setup_creator_config(creator_id, max_count)

    print("\n🚀 开始启动爬虫...")
    print("请准备手机扫码登录抖音!")
    print("-" * 60)

    # 重要提醒
    print("\n⚠️  重要提醒:")
    print("1. 程序会先打开浏览器，请扫码登录")
    print("2. 登录成功后，请手动浏览几个视频（至少2-3个）")
    print("3. 这样可以模拟正常用户行为，避免被检测")
    print("4. 浏览完成后，回到这里按回车继续")
    print()

    input("理解上述要求，按回车开始...")

    # 预启动延迟，模拟人工操作
    print("⏰ 预启动准备中，等待10秒...")
    import time
    time.sleep(10)

    # 增强的错误处理和重试机制
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            if retry_count > 0:
                print(f"\n🔄 第 {retry_count + 1} 次尝试...")
                print("⏰ 等待60秒后重试，避免被检测...")
                import time
                time.sleep(60)
            else:
                # 首次运行时的特殊处理
                print("\n🌐 浏览器即将启动...")
                print("请完成以下步骤:")
                print("1. 扫码登录抖音")
                print("2. 手动浏览2-3个视频（重要！）")
                print("3. 完成后回到这里")
                print()

                # 先启动爬虫让用户登录
                print("正在启动浏览器...")

            # 运行爬虫
            await crawler_main()

            print("\n🎉 爬取完成!")
            print("📁 原始数据保存在 data/douyin/ 目录下")

            # 提取指定类型的链接
            print("\n🔗 正在提取链接...")
            data = load_crawled_data()

            # 如果是创作者模式，先验证和筛选视频
            if crawl_mode == 2:
                print("🔍 创作者模式: 验证视频归属...")
                data, invalid_videos = validate_and_filter_creator_videos(data, creator_id)

                if not data:
                    print("❌ 没有找到属于目标创作者的视频!")
                    print("💡 建议:")
                    print("   1. 检查抖音号是否正确")
                    print("   2. 使用用户主页链接而非数字抖音号")
                    print("   3. 确认该创作者有公开视频")
                    return
                elif invalid_videos:
                    print(f"⚠️  已过滤掉 {len(invalid_videos)} 个不相关的视频")

            extract_links_by_type(data, link_type)

            print("\n✅ 链接提取完成!")
            if crawl_mode == 1:
                print("💡 提示: 已从搜索结果中提取链接")
            else:
                print(f"💡 提示: 已从指定抖音号中提取 {len(data)} 个有效视频的链接")
            print("📁 链接文件已保存在当前目录下")

            # 成功完成，跳出重试循环
            break

        except Exception as e:
            error_msg = str(e).lower()
            retry_count += 1

            print(f"\n❌ 爬取过程中出现错误: {e}")

            if "account blocked" in error_msg or "blocked" in error_msg:
                print("🚫 检测到账号被限制访问")
                print("💡 解决建议:")
                print("   1. 等待更长时间后重试（建议等待1-2小时）")
                print("   2. 更换网络环境或使用代理")
                print("   3. 确保已正确扫码登录抖音账号")
                print("   4. 尝试在手机抖音APP中正常浏览该用户主页")

                if retry_count < max_retries:
                    print(f"   5. 将在60秒后自动重试 ({retry_count}/{max_retries})")
                else:
                    print("   5. 已达到最大重试次数，请稍后手动重试")
                    break

            elif "network" in error_msg or "connection" in error_msg:
                print("🌐 网络连接问题")
                print("💡 请检查网络连接状态")

                if retry_count < max_retries:
                    print(f"   将在60秒后自动重试 ({retry_count}/{max_retries})")
                else:
                    print("   已达到最大重试次数，请检查网络后重试")
                    break

            else:
                print("💡 其他错误，请检查日志信息")

                if retry_count < max_retries:
                    print(f"   将在60秒后自动重试 ({retry_count}/{max_retries})")
                else:
                    print("   已达到最大重试次数")
                    break


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")
